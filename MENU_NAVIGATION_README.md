# Menu Navigation - Dokumentacja

## Opis funkcjonalności

Dodano funkcjonalność automatycznego podkreślania aktywnych elementów menu w dwóch wariantach:

1. **Kotwice HTML** - dla sekcji na stronie głównej (#home, #oskar<PERSON>, #dom<PERSON>, #zd<PERSON><PERSON>, #kontakt)
2. **Podstrony** - dla odnośników do podstron (np. /oferta)

## Pliki zmodyfikowane

### 1. `assets/css/style.css`
Dodano style CSS dla aktywnych elementów menu:

```css
/* Podstawowe style dla elementów menu */
.e-n-menu-title-container {
    position: relative;
    transition: all 0.3s ease;
}

/* Podkreślenie dla aktywnego elementu menu */
.e-n-menu-title-container::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #c59156;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

/* Aktywny element menu - podkreślenie */
.e-n-menu-title.menu-active .e-n-menu-title-container::after,
.e-n-menu-title.e-current .e-n-menu-title-container::after {
    width: 100%;
}
```

### 2. `assets/js/menu-navigation.js` (NOWY PLIK)
Główna logika obsługi aktywnych stanów menu:

- **Klasa MenuNavigationHandler** - zarządza całą funkcjonalnością
- **Wykrywanie aktywnych sekcji** - podczas przewijania strony
- **Obsługa podstron** - wykrywanie aktualnej ścieżki URL
- **Smooth scroll** - płynne przewijanie do sekcji po kliknięciu
- **Responsywność** - optymalizacja dla urządzeń mobilnych

### 3. `includes/enqueue.php`
Dodano ładowanie nowego pliku JavaScript:

```php
wp_register_script('menu-navigation', DD_PLUGIN_DIR_URL . '/assets/js/menu-navigation.js', array(), DD_PLUGIN_SKARPA_VERSION, 1);
wp_enqueue_script('menu-navigation');
```

## Jak to działa

### Dla kotwic HTML (sekcje na stronie)
1. Skrypt automatycznie wykrywa sekcje z ID: `home`, `oskarpie`, `domki`, `zdjecia`, `kontakt`
2. Podczas przewijania strony sprawdza, która sekcja jest aktualnie widoczna
3. Dodaje klasę `menu-active` do odpowiedniego elementu menu
4. CSS wyświetla podkreślenie pod aktywnym elementem

### Dla podstron
1. Skrypt sprawdza aktualną ścieżkę URL (`window.location.pathname`)
2. Jeśli ścieżka zawiera `/oferta`, aktywuje odpowiedni element menu
3. Podkreślenie pozostaje aktywne podczas przebywania na podstronie

### Funkcje dodatkowe
- **Smooth scroll** - płynne przewijanie po kliknięciu w menu
- **Throttling** - optymalizacja wydajności podczas przewijania
- **Obsługa historii przeglądarki** - prawidłowe działanie przycisków wstecz/dalej
- **Responsywność** - dostosowanie do różnych rozmiarów ekranu

## Konfiguracja

### Dodawanie nowych sekcji
Aby dodać nową sekcję do automatycznego wykrywania, edytuj tablicę `sectionIds` w pliku `menu-navigation.js`:

```javascript
const sectionIds = ['home', 'oskarpie', 'domki', 'zdjecia', 'kontakt', 'nowa-sekcja'];
```

### Dodawanie nowych podstron
Aby dodać nową podstronę, edytuj metodę `checkCurrentPage()` w pliku `menu-navigation.js`:

```javascript
if (currentPath.includes('/oferta') || currentPath.includes('/nowa-podstrona')) {
    // logika obsługi
}
```

### Dostosowanie stylów
Kolory i animacje można dostosować w pliku `style.css`:

```css
.e-n-menu-title-container::after {
    background-color: #c59156; /* Zmień kolor podkreślenia */
    height: 2px; /* Zmień grubość podkreślenia */
    transition: all 0.3s ease; /* Zmień czas animacji */
}
```

## Kompatybilność

- **WordPress** - kompatybilne z WordPress
- **Elementor** - działa z menu Elementor
- **Przeglądarki** - wszystkie nowoczesne przeglądarki
- **Urządzenia mobilne** - w pełni responsywne

## Debugowanie

Aby sprawdzić działanie skryptu, otwórz konsolę przeglądarki i użyj:

```javascript
// Sprawdź czy skrypt jest załadowany
console.log(window.menuNavigation);

// Ręcznie odśwież stan menu
window.menuNavigation.refresh();

// Sprawdź aktywne elementy menu
document.querySelectorAll('.e-n-menu-title.menu-active');
```

## Wydajność

Skrypt został zoptymalizowany pod kątem wydajności:
- Używa `requestAnimationFrame` dla płynnych animacji
- Implementuje throttling dla zdarzeń scroll
- Minimalizuje manipulacje DOM
- Używa event delegation gdzie to możliwe
