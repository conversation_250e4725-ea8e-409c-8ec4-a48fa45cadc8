/**
 * Menu Navigation Active States Handler
 * Obsługuje podkreślanie aktywnych elementów menu dla:
 * 1. Kotwic HTML (#home, #oskarpie, #domki, #zdjecia, #kontakt)
 * 2. Podstron (/oferta)
 */

class MenuNavigationHandler {
    constructor() {
        this.menuItems = [];
        this.sections = [];
        this.isScrolling = false;
        this.scrollTimeout = null;
        
        this.init();
    }

    init() {
        // Czekaj na załadowanie DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.collectMenuItems();
        this.collectSections();
        this.bindEvents();
        this.checkInitialState();
    }

    collectMenuItems() {
        this.menuItems = Array.from(document.querySelectorAll('.e-n-menu-title'));
    }

    collectSections() {
        const sectionIds = ['home', 'oskarpie', 'domki', 'zdjecia', 'kontakt'];
        
        this.sections = sectionIds.map(id => {
            const element = document.getElementById(id);
            const menuItem = document.querySelector(`a[href="#${id}"]`)?.closest('.e-n-menu-title');
            
            return {
                id,
                element,
                menuItem,
                isValid: element && menuItem
            };
        }).filter(section => section.isValid);
    }

    bindEvents() {
        // Scroll event z throttling
        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        
        // Click events dla smooth scroll
        this.menuItems.forEach(item => {
            const link = item.querySelector('a');
            if (link && link.getAttribute('href')?.startsWith('#')) {
                link.addEventListener('click', this.handleMenuClick.bind(this, item, link));
            }
        });

        // Obsługa zmian w historii przeglądarki (back/forward)
        window.addEventListener('popstate', () => {
            setTimeout(() => this.checkInitialState(), 100);
        });
    }

    handleScroll() {
        if (this.isScrolling) return;
        
        this.isScrolling = true;
        requestAnimationFrame(() => {
            this.checkActiveSection();
            this.isScrolling = false;
        });
    }

    handleMenuClick(menuItem, link, event) {
        const targetId = link.getAttribute('href').substring(1);
        const targetSection = document.getElementById(targetId);
        
        if (!targetSection) return;
        
        event.preventDefault();
        
        // Smooth scroll do sekcji
        targetSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
        
        // Ustaw aktywny element po krótkiej chwili
        setTimeout(() => {
            this.setActiveMenuItem(menuItem);
        }, 150);
    }

    clearActiveStates() {
        this.menuItems.forEach(item => {
            item.classList.remove('menu-active');
        });
    }

    setActiveMenuItem(activeItem) {
        this.clearActiveStates();
        if (activeItem) {
            activeItem.classList.add('menu-active');
        }
    }

    checkCurrentPage() {
        const currentPath = window.location.pathname;
        
        // Sprawdź różne warianty ścieżki do oferty
        if (currentPath.includes('/oferta') || currentPath.endsWith('/oferta/')) {
            const offerMenuItem = document.querySelector('a[href*="/oferta"]')?.closest('.e-n-menu-title');
            if (offerMenuItem) {
                this.setActiveMenuItem(offerMenuItem);
                return true;
            }
        }
        
        return false;
    }

    checkActiveSection() {
        // Jeśli jesteśmy na podstronie, nie sprawdzaj sekcji
        if (this.checkCurrentPage()) {
            return;
        }

        const scrollPosition = window.scrollY + window.innerHeight * 0.1; // 10% wysokości okna jako offset
        let activeSection = null;
        let closestDistance = Infinity;

        // Znajdź najbliższą sekcję
        this.sections.forEach(section => {
            if (!section.element) return;
            
            const rect = section.element.getBoundingClientRect();
            const sectionTop = window.scrollY + rect.top;
            const sectionBottom = sectionTop + rect.height;
            
            // Sprawdź czy sekcja jest w viewport
            if (scrollPosition >= sectionTop && scrollPosition <= sectionBottom) {
                const distance = Math.abs(scrollPosition - sectionTop);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    activeSection = section;
                }
            }
        });

        // Jeśli nie znaleziono aktywnej sekcji i jesteśmy blisko góry strony
        if (!activeSection && window.scrollY < 200) {
            activeSection = this.sections.find(s => s.id === 'home');
        }

        // Ustaw aktywny element menu
        if (activeSection && activeSection.menuItem) {
            this.setActiveMenuItem(activeSection.menuItem);
        } else if (!this.checkCurrentPage()) {
            // Jeśli nie ma aktywnej sekcji i nie jesteśmy na podstronie, wyczyść aktywne stany
            this.clearActiveStates();
        }
    }

    checkInitialState() {
        // Sprawdź hash w URL
        const hash = window.location.hash;
        if (hash) {
            const targetSection = document.querySelector(hash);
            const menuItem = document.querySelector(`a[href="${hash}"]`)?.closest('.e-n-menu-title');
            
            if (targetSection && menuItem) {
                this.setActiveMenuItem(menuItem);
                return;
            }
        }
        
        // Sprawdź czy jesteśmy na podstronie
        if (!this.checkCurrentPage()) {
            // Sprawdź aktywną sekcję na podstawie pozycji scroll
            this.checkActiveSection();
        }
    }

    // Publiczna metoda do ręcznego odświeżenia stanu menu
    refresh() {
        this.collectMenuItems();
        this.collectSections();
        this.checkInitialState();
    }
}

// Inicjalizacja
const menuNavigation = new MenuNavigationHandler();

// Eksport dla ewentualnego użycia w innych skryptach
if (typeof window !== 'undefined') {
    window.MenuNavigationHandler = MenuNavigationHandler;
    window.menuNavigation = menuNavigation;
}
